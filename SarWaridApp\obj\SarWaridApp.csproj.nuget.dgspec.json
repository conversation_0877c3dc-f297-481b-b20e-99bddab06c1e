{"format": 1, "restore": {"E:\\SAR\\SarWaridApp\\SarWaridApp.csproj": {}}, "projects": {"E:\\SAR\\SarWaridApp\\SarWaridApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\SAR\\SarWaridApp\\SarWaridApp.csproj", "projectName": "SarWaridApp", "projectPath": "E:\\SAR\\SarWaridApp\\SarWaridApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\SAR\\SarWaridApp\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.Data.Sqlite": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}