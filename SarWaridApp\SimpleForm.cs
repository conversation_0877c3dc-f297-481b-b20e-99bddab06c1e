using System;
using System.Windows.Forms;
using SarWaridApp.Data;
using SarWaridApp.Models;

namespace SarWaridApp
{
    public partial class SimpleForm : Form
    {
        private DatabaseHelper _dbHelper;
        private TextBox textBoxDepartment;
        private TextBox textBoxSubject;
        private DateTimePicker dateTimePicker1;
        private RadioButton radioButtonIncoming;
        private RadioButton radioButtonOutgoing;
        private Button buttonAdd;
        private Button buttonRefresh;
        private DataGridView dataGridView1;
        private Label label1, label2, label3, label4;

        public SimpleForm()
        {
            InitializeComponent();
            _dbHelper = new DatabaseHelper();
            LoadDocuments();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام إدارة الصادر والوارد";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;

            // Labels
            label1 = new Label { Text = "التاريخ:", Location = new Point(700, 20), Size = new Size(60, 20) };
            label2 = new Label { Text = "اسم الدائرة:", Location = new Point(700, 50), Size = new Size(80, 20) };
            label3 = new Label { Text = "الموضوع:", Location = new Point(700, 80), Size = new Size(60, 20) };
            label4 = new Label { Text = "النوع:", Location = new Point(700, 110), Size = new Size(40, 20) };

            // Controls
            dateTimePicker1 = new DateTimePicker 
            { 
                Location = new Point(500, 20), 
                Size = new Size(180, 25),
                Value = DateTime.Today
            };

            textBoxDepartment = new TextBox 
            { 
                Location = new Point(400, 50), 
                Size = new Size(280, 25) 
            };

            textBoxSubject = new TextBox 
            { 
                Location = new Point(200, 80), 
                Size = new Size(480, 25) 
            };

            radioButtonIncoming = new RadioButton 
            { 
                Text = "وارد", 
                Location = new Point(600, 110), 
                Size = new Size(60, 20),
                Checked = true
            };

            radioButtonOutgoing = new RadioButton 
            { 
                Text = "صادر", 
                Location = new Point(530, 110), 
                Size = new Size(60, 20) 
            };

            buttonAdd = new Button 
            { 
                Text = "إضافة", 
                Location = new Point(400, 140), 
                Size = new Size(80, 30) 
            };
            buttonAdd.Click += ButtonAdd_Click;

            buttonRefresh = new Button 
            { 
                Text = "تحديث", 
                Location = new Point(300, 140), 
                Size = new Size(80, 30) 
            };
            buttonRefresh.Click += ButtonRefresh_Click;

            dataGridView1 = new DataGridView 
            { 
                Location = new Point(20, 180), 
                Size = new Size(740, 350),
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };

            // Add controls to form
            this.Controls.AddRange(new Control[] 
            {
                label1, label2, label3, label4,
                dateTimePicker1, textBoxDepartment, textBoxSubject,
                radioButtonIncoming, radioButtonOutgoing,
                buttonAdd, buttonRefresh, dataGridView1
            });
        }

        private void ButtonAdd_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(textBoxDepartment.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الدائرة!");
                return;
            }

            if (string.IsNullOrWhiteSpace(textBoxSubject.Text))
            {
                MessageBox.Show("يرجى إدخال الموضوع!");
                return;
            }

            var document = new Document
            {
                Date = dateTimePicker1.Value.Date,
                DepartmentName = textBoxDepartment.Text.Trim(),
                Subject = textBoxSubject.Text.Trim(),
                Type = radioButtonIncoming.Checked ? DocumentType.Incoming : DocumentType.Outgoing
            };

            var id = _dbHelper.AddDocument(document);
            if (id > 0)
            {
                MessageBox.Show("تم إضافة الوثيقة بنجاح!");
                LoadDocuments();
                ClearForm();
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء إضافة الوثيقة!");
            }
        }

        private void ButtonRefresh_Click(object sender, EventArgs e)
        {
            LoadDocuments();
        }

        private void LoadDocuments()
        {
            try
            {
                var documents = _dbHelper.GetAllDocuments();
                dataGridView1.DataSource = documents;

                if (dataGridView1.Columns.Count > 0)
                {
                    dataGridView1.Columns["Id"].HeaderText = "التسلسل";
                    dataGridView1.Columns["Date"].HeaderText = "التاريخ";
                    dataGridView1.Columns["DepartmentName"].HeaderText = "اسم الدائرة";
                    dataGridView1.Columns["Subject"].HeaderText = "الموضوع";
                    dataGridView1.Columns["Type"].HeaderText = "النوع";
                    
                    if (dataGridView1.Columns["CreatedAt"] != null)
                        dataGridView1.Columns["CreatedAt"].Visible = false;
                    if (dataGridView1.Columns["UpdatedAt"] != null)
                        dataGridView1.Columns["UpdatedAt"].Visible = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private void ClearForm()
        {
            textBoxDepartment.Clear();
            textBoxSubject.Clear();
            dateTimePicker1.Value = DateTime.Today;
            radioButtonIncoming.Checked = true;
        }
    }
}
