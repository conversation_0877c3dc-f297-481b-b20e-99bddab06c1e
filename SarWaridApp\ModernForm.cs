using System;
using System.Drawing;
using System.Windows.Forms;
using SarWaridApp.Data;
using SarWaridApp.Models;

namespace SarWaridApp
{
    public partial class ModernForm : Form
    {
        private DatabaseHelper _dbHelper;
        private TabControl mainTabControl;
        private TabPage incomingTab, outgoingTab;

        // Incoming Document Controls
        private TextBox txtIncomingDepartment, txtIncomingNumber, txtIncomingSubject;
        private DateTimePicker dtpIncomingDate, dtpIncomingDocDate;
        private Button btnAddIncoming, btnRefreshIncoming, btnSearchIncoming, btnExportIncoming;
        private TextBox txtSearchIncoming;
        private DataGridView dgvIncoming;
        private Label lblIncomingStats;

        // Outgoing Document Controls
        private TextBox txtOutgoingDepartment, txtOutgoingSubject;
        private DateTimePicker dtpOutgoingDate;
        private Button btnAddOutgoing, btnRefreshOutgoing, btnSearchOutgoing, btnExportOutgoing;
        private TextBox txtSearchOutgoing;
        private DataGridView dgvOutgoing;
        private Label lblOutgoingStats;

        public ModernForm()
        {
            InitializeComponent();
            _dbHelper = new DatabaseHelper();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام إدارة الصادر والوارد - الإصدار الحديث";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 244, 248);
            this.Font = new Font("Segoe UI", 10F);

            CreateModernInterface();
        }

        private void CreateModernInterface()
        {
            // Main Tab Control
            mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Padding = new Point(20, 8)
            };

            // Incoming Documents Tab
            incomingTab = new TabPage("📥 الوثائق الواردة")
            {
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            CreateIncomingTab();

            // Outgoing Documents Tab
            outgoingTab = new TabPage("📤 الوثائق الصادرة")
            {
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            CreateOutgoingTab();

            mainTabControl.TabPages.Add(incomingTab);
            mainTabControl.TabPages.Add(outgoingTab);
            this.Controls.Add(mainTabControl);
        }

        private void CreateIncomingTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.FromArgb(245, 245, 245)
            };

            // Title
            var titleLabel = new Label
            {
                Text = "📥 إدارة الوثائق الواردة",
                Font = new Font("Tahoma", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 77, 64),
                Location = new Point(20, 20),
                Size = new Size(400, 35)
            };

            // Statistics Label
            lblIncomingStats = new Label
            {
                Text = "📊 إجمالي الوثائق: 0",
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 77, 64),
                Location = new Point(500, 25),
                Size = new Size(400, 25)
            };

            // Create Table-Style Input Form
            var inputTable = CreateIncomingInputTable();
            inputTable.Location = new Point(50, 70);

            // Buttons Panel
            var buttonsPanel = new Panel
            {
                Location = new Point(50, 320),
                Size = new Size(1200, 50),
                BackColor = Color.Transparent
            };

            // Buttons
            btnAddIncoming = CreateButton("➕ إضافة", 0, 10, Color.FromArgb(46, 204, 113));
            btnRefreshIncoming = CreateButton("🔄 تحديث", 120, 10, Color.FromArgb(52, 152, 219));
            btnSearchIncoming = CreateButton("🔍 بحث", 240, 10, Color.FromArgb(155, 89, 182));
            btnExportIncoming = CreateButton("📊 تصدير", 360, 10, Color.FromArgb(230, 126, 34));

            btnAddIncoming.Click += BtnAddIncoming_Click;
            btnRefreshIncoming.Click += BtnRefreshIncoming_Click;
            btnSearchIncoming.Click += BtnSearchIncoming_Click;
            btnExportIncoming.Click += BtnExportIncoming_Click;

            buttonsPanel.Controls.AddRange(new Control[] {
                btnAddIncoming, btnRefreshIncoming, btnSearchIncoming, btnExportIncoming
            });

            // Search Box
            var searchGroup = new GroupBox
            {
                Text = "البحث في الوثائق الواردة",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(20, 280),
                Size = new Size(1300, 60),
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            var lblSearch = CreateLabel("البحث:", 30, 25);
            txtSearchIncoming = CreateTextBox(80, 22, 400);
            var btnClearSearch = CreateButton("🗑️ مسح", 500, 20, Color.FromArgb(231, 76, 60));
            btnClearSearch.Click += (s, e) => { txtSearchIncoming.Clear(); LoadIncomingDocuments(); };

            searchGroup.Controls.AddRange(new Control[] { lblSearch, txtSearchIncoming, btnClearSearch });

            // Data Grid
            dgvIncoming = new DataGridView
            {
                Location = new Point(20, 350),
                Size = new Size(1300, 340),
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 9F),
                    SelectionBackColor = Color.FromArgb(52, 152, 219),
                    SelectionForeColor = Color.White
                }
            };

            panel.Controls.AddRange(new Control[] { titleLabel, lblIncomingStats, inputTable, buttonsPanel, searchGroup, dgvIncoming });
            incomingTab.Controls.Add(panel);
        }

        private void CreateOutgoingTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Title
            var titleLabel = new Label
            {
                Text = "إدارة الوثائق الصادرة",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(300, 35)
            };

            // Statistics Label
            lblOutgoingStats = new Label
            {
                Text = "📊 إجمالي الوثائق: 0",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(400, 25),
                Size = new Size(300, 25)
            };

            // Input Group Box
            var inputGroup = new GroupBox
            {
                Text = "إدخال وثيقة صادرة جديدة",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(1300, 150),
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            // Row 1
            var lblSerial = CreateLabel("التسلسل: تلقائي", 30, 40);
            var lblDate = CreateLabel("التاريخ:", 350, 40);
            dtpOutgoingDate = CreateDatePicker(420, 37);

            var lblDepartment = CreateLabel("اسم الدائرة:", 700, 40);
            txtOutgoingDepartment = CreateTextBox(800, 37, 400);

            // Row 2
            var lblSubject = CreateLabel("الموضوع:", 30, 80);
            txtOutgoingSubject = CreateTextBox(100, 77, 1000);

            // Buttons
            btnAddOutgoing = CreateButton("➕ إضافة", 30, 110, Color.FromArgb(46, 204, 113));
            btnRefreshOutgoing = CreateButton("🔄 تحديث", 150, 110, Color.FromArgb(52, 152, 219));
            btnSearchOutgoing = CreateButton("🔍 بحث", 270, 110, Color.FromArgb(155, 89, 182));
            btnExportOutgoing = CreateButton("📊 تصدير", 390, 110, Color.FromArgb(230, 126, 34));

            btnAddOutgoing.Click += BtnAddOutgoing_Click;
            btnRefreshOutgoing.Click += BtnRefreshOutgoing_Click;
            btnSearchOutgoing.Click += BtnSearchOutgoing_Click;
            btnExportOutgoing.Click += BtnExportOutgoing_Click;

            inputGroup.Controls.AddRange(new Control[] {
                lblSerial, lblDate, dtpOutgoingDate, lblDepartment, txtOutgoingDepartment,
                lblSubject, txtOutgoingSubject, btnAddOutgoing, btnRefreshOutgoing,
                btnSearchOutgoing, btnExportOutgoing
            });

            // Search Box for Outgoing
            var searchGroupOut = new GroupBox
            {
                Text = "البحث في الوثائق الصادرة",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(20, 230),
                Size = new Size(1300, 60),
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            var lblSearchOut = CreateLabel("البحث:", 30, 25);
            txtSearchOutgoing = CreateTextBox(80, 22, 400);
            var btnClearSearchOut = CreateButton("🗑️ مسح", 500, 20, Color.FromArgb(231, 76, 60));
            btnClearSearchOut.Click += (s, e) => { txtSearchOutgoing.Clear(); LoadOutgoingDocuments(); };

            searchGroupOut.Controls.AddRange(new Control[] { lblSearchOut, txtSearchOutgoing, btnClearSearchOut });

            // Data Grid
            dgvOutgoing = new DataGridView
            {
                Location = new Point(20, 300),
                Size = new Size(1300, 390),
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 9F),
                    SelectionBackColor = Color.FromArgb(52, 152, 219),
                    SelectionForeColor = Color.White
                }
            };

            panel.Controls.AddRange(new Control[] { titleLabel, lblOutgoingStats, inputGroup, searchGroupOut, dgvOutgoing });
            outgoingTab.Controls.Add(panel);
        }

        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
        }

        private TextBox CreateTextBox(int x, int y, int width)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 25),
                Font = new Font("Segoe UI", 10F),
                BorderStyle = BorderStyle.FixedSingle
            };
        }

        private DateTimePicker CreateDatePicker(int x, int y)
        {
            return new DateTimePicker
            {
                Location = new Point(x, y),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10F),
                Value = DateTime.Today
            };
        }

        private Button CreateButton(string text, int x, int y, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(100, 35),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
        }

        private Panel CreateIncomingInputTable()
        {
            var tablePanel = new Panel
            {
                Size = new Size(1200, 240),
                BackColor = Color.Transparent
            };

            // Create table rows with the exact design from the image
            var rows = new[]
            {
                new { Label = "التسلسل", Value = "تلقائي", IsReadOnly = true },
                new { Label = "التاريخ", Value = "", IsReadOnly = false },
                new { Label = "الدائرة", Value = "", IsReadOnly = false },
                new { Label = "العدد", Value = "", IsReadOnly = false },
                new { Label = "تاريخ", Value = "", IsReadOnly = false },
                new { Label = "الموضوع", Value = "", IsReadOnly = false }
            };

            for (int i = 0; i < rows.Length; i++)
            {
                var row = CreateTableRow(rows[i].Label, rows[i].Value, i, rows[i].IsReadOnly);
                row.Location = new Point(0, i * 40);
                tablePanel.Controls.Add(row);
            }

            return tablePanel;
        }

        private Panel CreateTableRow(string labelText, string value, int index, bool isReadOnly)
        {
            var rowPanel = new Panel
            {
                Size = new Size(1200, 40),
                BackColor = Color.Transparent
            };

            // Label part (right side) - Dark green background
            var labelPanel = new Panel
            {
                Size = new Size(200, 38),
                Location = new Point(1000, 1),
                BackColor = Color.FromArgb(0, 77, 64),
                BorderStyle = BorderStyle.FixedSingle
            };

            var label = new Label
            {
                Text = labelText,
                Font = new Font("Tahoma", 12F, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            labelPanel.Controls.Add(label);

            // Value part (left side) - White background
            var valuePanel = new Panel
            {
                Size = new Size(800, 38),
                Location = new Point(200, 1),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            if (index == 0) // Serial number - display only
            {
                var serialLabel = new Label
                {
                    Text = "تلقائي",
                    Font = new Font("Tahoma", 12F),
                    ForeColor = Color.Gray,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Dock = DockStyle.Fill
                };
                valuePanel.Controls.Add(serialLabel);
            }
            else if (index == 1) // Date picker
            {
                dtpIncomingDate = new DateTimePicker
                {
                    Font = new Font("Tahoma", 12F),
                    Format = DateTimePickerFormat.Short,
                    Value = DateTime.Today,
                    Location = new Point(10, 8),
                    Size = new Size(780, 22)
                };
                valuePanel.Controls.Add(dtpIncomingDate);
            }
            else if (index == 2) // Department
            {
                txtIncomingDepartment = new TextBox
                {
                    Font = new Font("Tahoma", 12F),
                    Location = new Point(10, 8),
                    Size = new Size(780, 22),
                    BorderStyle = BorderStyle.None
                };
                valuePanel.Controls.Add(txtIncomingDepartment);
            }
            else if (index == 3) // Number
            {
                txtIncomingNumber = new TextBox
                {
                    Font = new Font("Tahoma", 12F),
                    Location = new Point(10, 8),
                    Size = new Size(780, 22),
                    BorderStyle = BorderStyle.None
                };
                valuePanel.Controls.Add(txtIncomingNumber);
            }
            else if (index == 4) // Incoming Date
            {
                dtpIncomingDocDate = new DateTimePicker
                {
                    Font = new Font("Tahoma", 12F),
                    Format = DateTimePickerFormat.Short,
                    Value = DateTime.Today,
                    Location = new Point(10, 8),
                    Size = new Size(780, 22)
                };
                valuePanel.Controls.Add(dtpIncomingDocDate);
            }
            else if (index == 5) // Subject
            {
                txtIncomingSubject = new TextBox
                {
                    Font = new Font("Tahoma", 12F),
                    Location = new Point(10, 8),
                    Size = new Size(780, 22),
                    BorderStyle = BorderStyle.None
                };
                valuePanel.Controls.Add(txtIncomingSubject);
            }

            rowPanel.Controls.Add(labelPanel);
            rowPanel.Controls.Add(valuePanel);

            return rowPanel;
        }

        private void BtnAddIncoming_Click(object sender, EventArgs e)
        {
            if (!ValidateIncomingInput()) return;

            var document = new IncomingDocument
            {
                Date = dtpIncomingDate.Value.Date,
                DepartmentName = txtIncomingDepartment.Text.Trim(),
                IncomingDate = dtpIncomingDocDate.Value.Date,
                Number = txtIncomingNumber.Text.Trim(),
                Subject = txtIncomingSubject.Text.Trim()
            };

            var id = _dbHelper.AddIncomingDocument(document);
            if (id > 0)
            {
                ShowSuccessMessage("تم إضافة الوثيقة الواردة بنجاح! ✅");
                LoadIncomingDocuments();
                ClearIncomingForm();
            }
            else
            {
                ShowErrorMessage("حدث خطأ أثناء إضافة الوثيقة! ❌");
            }
        }

        private void BtnAddOutgoing_Click(object sender, EventArgs e)
        {
            if (!ValidateOutgoingInput()) return;

            var document = new OutgoingDocument
            {
                Date = dtpOutgoingDate.Value.Date,
                DepartmentName = txtOutgoingDepartment.Text.Trim(),
                Subject = txtOutgoingSubject.Text.Trim()
            };

            var id = _dbHelper.AddOutgoingDocument(document);
            if (id > 0)
            {
                ShowSuccessMessage("تم إضافة الوثيقة الصادرة بنجاح! ✅");
                LoadOutgoingDocuments();
                ClearOutgoingForm();
            }
            else
            {
                ShowErrorMessage("حدث خطأ أثناء إضافة الوثيقة! ❌");
            }
        }

        private void BtnRefreshIncoming_Click(object sender, EventArgs e)
        {
            LoadIncomingDocuments();
            ShowInfoMessage("تم تحديث قائمة الوثائق الواردة 🔄");
        }

        private void BtnRefreshOutgoing_Click(object sender, EventArgs e)
        {
            LoadOutgoingDocuments();
            ShowInfoMessage("تم تحديث قائمة الوثائق الصادرة 🔄");
        }

        private void LoadData()
        {
            LoadIncomingDocuments();
            LoadOutgoingDocuments();
        }

        private void LoadIncomingDocuments()
        {
            try
            {
                var documents = _dbHelper.GetAllIncomingDocuments();
                var displayData = documents.Select(d => new
                {
                    التسلسل = d.Id,
                    التاريخ = d.Date.ToString("yyyy/MM/dd"),
                    اسم_الدائرة = d.DepartmentName,
                    تاريخ_الوارد = d.IncomingDate.ToString("yyyy/MM/dd"),
                    العدد = d.Number,
                    الموضوع = d.Subject
                }).ToList();

                dgvIncoming.DataSource = displayData;
                FormatDataGridView(dgvIncoming);
                UpdateIncomingStats(documents.Count);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في تحميل الوثائق الواردة: {ex.Message}");
            }
        }

        private void LoadOutgoingDocuments()
        {
            try
            {
                var documents = _dbHelper.GetAllOutgoingDocuments();
                var displayData = documents.Select(d => new
                {
                    التسلسل = d.Id,
                    التاريخ = d.Date.ToString("yyyy/MM/dd"),
                    اسم_الدائرة = d.DepartmentName,
                    الموضوع = d.Subject
                }).ToList();

                dgvOutgoing.DataSource = displayData;
                FormatDataGridView(dgvOutgoing);
                UpdateOutgoingStats(documents.Count);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في تحميل الوثائق الصادرة: {ex.Message}");
            }
        }

        private void FormatDataGridView(DataGridView dgv)
        {
            if (dgv.Columns.Count > 0)
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                }

                if (dgv.Columns["التسلسل"] != null)
                    dgv.Columns["التسلسل"].FillWeight = 10;
                if (dgv.Columns["التاريخ"] != null)
                    dgv.Columns["التاريخ"].FillWeight = 15;
                if (dgv.Columns["اسم_الدائرة"] != null)
                    dgv.Columns["اسم_الدائرة"].FillWeight = 25;
                if (dgv.Columns["الموضوع"] != null)
                    dgv.Columns["الموضوع"].FillWeight = 40;
            }
        }

        private bool ValidateIncomingInput()
        {
            if (string.IsNullOrWhiteSpace(txtIncomingDepartment.Text))
            {
                ShowWarningMessage("يرجى إدخال اسم الدائرة! ⚠️");
                txtIncomingDepartment.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtIncomingNumber.Text))
            {
                ShowWarningMessage("يرجى إدخال العدد! ⚠️");
                txtIncomingNumber.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtIncomingSubject.Text))
            {
                ShowWarningMessage("يرجى إدخال الموضوع! ⚠️");
                txtIncomingSubject.Focus();
                return false;
            }

            return true;
        }

        private bool ValidateOutgoingInput()
        {
            if (string.IsNullOrWhiteSpace(txtOutgoingDepartment.Text))
            {
                ShowWarningMessage("يرجى إدخال اسم الدائرة! ⚠️");
                txtOutgoingDepartment.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtOutgoingSubject.Text))
            {
                ShowWarningMessage("يرجى إدخال الموضوع! ⚠️");
                txtOutgoingSubject.Focus();
                return false;
            }

            return true;
        }

        private void ClearIncomingForm()
        {
            txtIncomingDepartment.Clear();
            txtIncomingNumber.Clear();
            txtIncomingSubject.Clear();
            dtpIncomingDate.Value = DateTime.Today;
            dtpIncomingDocDate.Value = DateTime.Today;
        }

        private void ClearOutgoingForm()
        {
            txtOutgoingDepartment.Clear();
            txtOutgoingSubject.Clear();
            dtpOutgoingDate.Value = DateTime.Today;
        }

        private void ShowSuccessMessage(string message)
        {
            MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void ShowWarningMessage(string message)
        {
            MessageBox.Show(message, "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private void ShowInfoMessage(string message)
        {
            MessageBox.Show(message, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void UpdateIncomingStats(int count)
        {
            lblIncomingStats.Text = $"📊 إجمالي الوثائق الواردة: {count} | 📅 اليوم: {DateTime.Today:yyyy/MM/dd}";
        }

        private void UpdateOutgoingStats(int count)
        {
            lblOutgoingStats.Text = $"📊 إجمالي الوثائق الصادرة: {count} | 📅 اليوم: {DateTime.Today:yyyy/MM/dd}";
        }

        private void BtnSearchIncoming_Click(object sender, EventArgs e)
        {
            var searchTerm = txtSearchIncoming.Text.Trim();
            if (string.IsNullOrEmpty(searchTerm))
            {
                LoadIncomingDocuments();
                return;
            }

            try
            {
                var allDocuments = _dbHelper.GetAllIncomingDocuments();
                var filteredDocuments = allDocuments.Where(d =>
                    d.DepartmentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    d.Subject.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    d.Number.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ).ToList();

                var displayData = filteredDocuments.Select(d => new
                {
                    التسلسل = d.Id,
                    التاريخ = d.Date.ToString("yyyy/MM/dd"),
                    اسم_الدائرة = d.DepartmentName,
                    تاريخ_الوارد = d.IncomingDate.ToString("yyyy/MM/dd"),
                    العدد = d.Number,
                    الموضوع = d.Subject
                }).ToList();

                dgvIncoming.DataSource = displayData;
                FormatDataGridView(dgvIncoming);

                ShowInfoMessage($"تم العثور على {filteredDocuments.Count} وثيقة 🔍");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في البحث: {ex.Message}");
            }
        }

        private void BtnSearchOutgoing_Click(object sender, EventArgs e)
        {
            var searchTerm = txtSearchOutgoing.Text.Trim();
            if (string.IsNullOrEmpty(searchTerm))
            {
                LoadOutgoingDocuments();
                return;
            }

            try
            {
                var allDocuments = _dbHelper.GetAllOutgoingDocuments();
                var filteredDocuments = allDocuments.Where(d =>
                    d.DepartmentName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    d.Subject.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ).ToList();

                var displayData = filteredDocuments.Select(d => new
                {
                    التسلسل = d.Id,
                    التاريخ = d.Date.ToString("yyyy/MM/dd"),
                    اسم_الدائرة = d.DepartmentName,
                    الموضوع = d.Subject
                }).ToList();

                dgvOutgoing.DataSource = displayData;
                FormatDataGridView(dgvOutgoing);

                ShowInfoMessage($"تم العثور على {filteredDocuments.Count} وثيقة 🔍");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في البحث: {ex.Message}");
            }
        }

        private void BtnExportIncoming_Click(object sender, EventArgs e)
        {
            try
            {
                var documents = _dbHelper.GetAllIncomingDocuments();
                var csvContent = "التسلسل,التاريخ,اسم الدائرة,تاريخ الوارد,العدد,الموضوع\n";

                foreach (var doc in documents)
                {
                    csvContent += $"{doc.Id},{doc.Date:yyyy-MM-dd},\"{doc.DepartmentName}\",{doc.IncomingDate:yyyy-MM-dd},\"{doc.Number}\",\"{doc.Subject}\"\n";
                }

                var fileName = $"الوثائق_الواردة_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                File.WriteAllText(filePath, csvContent, System.Text.Encoding.UTF8);
                ShowSuccessMessage($"تم تصدير {documents.Count} وثيقة إلى سطح المكتب! 📊\n{fileName}");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في التصدير: {ex.Message}");
            }
        }

        private void BtnExportOutgoing_Click(object sender, EventArgs e)
        {
            try
            {
                var documents = _dbHelper.GetAllOutgoingDocuments();
                var csvContent = "التسلسل,التاريخ,اسم الدائرة,الموضوع\n";

                foreach (var doc in documents)
                {
                    csvContent += $"{doc.Id},{doc.Date:yyyy-MM-dd},\"{doc.DepartmentName}\",\"{doc.Subject}\"\n";
                }

                var fileName = $"الوثائق_الصادرة_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                File.WriteAllText(filePath, csvContent, System.Text.Encoding.UTF8);
                ShowSuccessMessage($"تم تصدير {documents.Count} وثيقة إلى سطح المكتب! 📊\n{fileName}");
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في التصدير: {ex.Message}");
            }
        }
    }
}
