using System;
using System.Drawing;
using System.Windows.Forms;
using SarWaridApp.Data;
using SarWaridApp.Models;

namespace SarWaridApp
{
    public partial class ModernForm : Form
    {
        private DatabaseHelper _dbHelper;
        private TabControl mainTabControl;
        private TabPage incomingTab, outgoingTab;

        // Incoming Document Controls
        private TextBox txtIncomingDepartment, txtIncomingNumber, txtIncomingSubject;
        private DateTimePicker dtpIncomingDate, dtpIncomingDocDate;
        private Button btnAddIncoming, btnRefreshIncoming, btnSearchIncoming, btnExportIncoming;
        private TextBox txtSearchIncoming;
        private DataGridView dgvIncoming;

        // Outgoing Document Controls
        private TextBox txtOutgoingDepartment, txtOutgoingSubject;
        private DateTimePicker dtpOutgoingDate;
        private Button btnAddOutgoing, btnRefreshOutgoing, btnSearchOutgoing, btnExportOutgoing;
        private TextBox txtSearchOutgoing;
        private DataGridView dgvOutgoing;

        public ModernForm()
        {
            InitializeComponent();
            _dbHelper = new DatabaseHelper();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام إدارة الصادر والوارد - الإصدار الحديث";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(240, 244, 248);
            this.Font = new Font("Segoe UI", 10F);

            CreateModernInterface();
        }

        private void CreateModernInterface()
        {
            // Main Tab Control
            mainTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Padding = new Point(20, 8)
            };

            // Incoming Documents Tab
            incomingTab = new TabPage("📥 الوثائق الواردة")
            {
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            CreateIncomingTab();

            // Outgoing Documents Tab
            outgoingTab = new TabPage("📤 الوثائق الصادرة")
            {
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            CreateOutgoingTab();

            mainTabControl.TabPages.Add(incomingTab);
            mainTabControl.TabPages.Add(outgoingTab);
            this.Controls.Add(mainTabControl);
        }

        private void CreateIncomingTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Title
            var titleLabel = new Label
            {
                Text = "إدارة الوثائق الواردة",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(300, 35)
            };

            // Input Group Box
            var inputGroup = new GroupBox
            {
                Text = "إدخال وثيقة واردة جديدة",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(1300, 200),
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            // Row 1
            var lblSerial = CreateLabel("التسلسل: تلقائي", 30, 40);
            var lblDate = CreateLabel("التاريخ:", 350, 40);
            dtpIncomingDate = CreateDatePicker(420, 37);

            var lblDepartment = CreateLabel("اسم الدائرة:", 700, 40);
            txtIncomingDepartment = CreateTextBox(800, 37, 400);

            // Row 2
            var lblIncomingDate = CreateLabel("تاريخ الوارد:", 30, 80);
            dtpIncomingDocDate = CreateDatePicker(130, 77);

            var lblNumber = CreateLabel("العدد:", 350, 80);
            txtIncomingNumber = CreateTextBox(400, 77, 200);

            // Row 3
            var lblSubject = CreateLabel("الموضوع:", 30, 120);
            txtIncomingSubject = CreateTextBox(100, 117, 1000);

            // Buttons
            btnAddIncoming = CreateButton("➕ إضافة", 30, 160, Color.FromArgb(46, 204, 113));
            btnRefreshIncoming = CreateButton("🔄 تحديث", 150, 160, Color.FromArgb(52, 152, 219));

            btnAddIncoming.Click += BtnAddIncoming_Click;
            btnRefreshIncoming.Click += BtnRefreshIncoming_Click;

            inputGroup.Controls.AddRange(new Control[] {
                lblSerial, lblDate, dtpIncomingDate, lblDepartment, txtIncomingDepartment,
                lblIncomingDate, dtpIncomingDocDate, lblNumber, txtIncomingNumber,
                lblSubject, txtIncomingSubject, btnAddIncoming, btnRefreshIncoming
            });

            // Data Grid
            dgvIncoming = new DataGridView
            {
                Location = new Point(20, 290),
                Size = new Size(1300, 400),
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 9F),
                    SelectionBackColor = Color.FromArgb(52, 152, 219),
                    SelectionForeColor = Color.White
                }
            };

            panel.Controls.AddRange(new Control[] { titleLabel, inputGroup, dgvIncoming });
            incomingTab.Controls.Add(panel);
        }

        private void CreateOutgoingTab()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Title
            var titleLabel = new Label
            {
                Text = "إدارة الوثائق الصادرة",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(300, 35)
            };

            // Input Group Box
            var inputGroup = new GroupBox
            {
                Text = "إدخال وثيقة صادرة جديدة",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(1300, 150),
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            // Row 1
            var lblSerial = CreateLabel("التسلسل: تلقائي", 30, 40);
            var lblDate = CreateLabel("التاريخ:", 350, 40);
            dtpOutgoingDate = CreateDatePicker(420, 37);

            var lblDepartment = CreateLabel("اسم الدائرة:", 700, 40);
            txtOutgoingDepartment = CreateTextBox(800, 37, 400);

            // Row 2
            var lblSubject = CreateLabel("الموضوع:", 30, 80);
            txtOutgoingSubject = CreateTextBox(100, 77, 1000);

            // Buttons
            btnAddOutgoing = CreateButton("➕ إضافة", 30, 110, Color.FromArgb(46, 204, 113));
            btnRefreshOutgoing = CreateButton("🔄 تحديث", 150, 110, Color.FromArgb(52, 152, 219));

            btnAddOutgoing.Click += BtnAddOutgoing_Click;
            btnRefreshOutgoing.Click += BtnRefreshOutgoing_Click;

            inputGroup.Controls.AddRange(new Control[] {
                lblSerial, lblDate, dtpOutgoingDate, lblDepartment, txtOutgoingDepartment,
                lblSubject, txtOutgoingSubject, btnAddOutgoing, btnRefreshOutgoing
            });

            // Data Grid
            dgvOutgoing = new DataGridView
            {
                Location = new Point(20, 240),
                Size = new Size(1300, 450),
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 9F),
                    SelectionBackColor = Color.FromArgb(52, 152, 219),
                    SelectionForeColor = Color.White
                }
            };

            panel.Controls.AddRange(new Control[] { titleLabel, inputGroup, dgvOutgoing });
            outgoingTab.Controls.Add(panel);
        }

        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10F),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
        }

        private TextBox CreateTextBox(int x, int y, int width)
        {
            return new TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 25),
                Font = new Font("Segoe UI", 10F),
                BorderStyle = BorderStyle.FixedSingle
            };
        }

        private DateTimePicker CreateDatePicker(int x, int y)
        {
            return new DateTimePicker
            {
                Location = new Point(x, y),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10F),
                Value = DateTime.Today
            };
        }

        private Button CreateButton(string text, int x, int y, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(100, 35),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
        }

        private void BtnAddIncoming_Click(object sender, EventArgs e)
        {
            if (!ValidateIncomingInput()) return;

            var document = new IncomingDocument
            {
                Date = dtpIncomingDate.Value.Date,
                DepartmentName = txtIncomingDepartment.Text.Trim(),
                IncomingDate = dtpIncomingDocDate.Value.Date,
                Number = txtIncomingNumber.Text.Trim(),
                Subject = txtIncomingSubject.Text.Trim()
            };

            var id = _dbHelper.AddIncomingDocument(document);
            if (id > 0)
            {
                ShowSuccessMessage("تم إضافة الوثيقة الواردة بنجاح! ✅");
                LoadIncomingDocuments();
                ClearIncomingForm();
            }
            else
            {
                ShowErrorMessage("حدث خطأ أثناء إضافة الوثيقة! ❌");
            }
        }

        private void BtnAddOutgoing_Click(object sender, EventArgs e)
        {
            if (!ValidateOutgoingInput()) return;

            var document = new OutgoingDocument
            {
                Date = dtpOutgoingDate.Value.Date,
                DepartmentName = txtOutgoingDepartment.Text.Trim(),
                Subject = txtOutgoingSubject.Text.Trim()
            };

            var id = _dbHelper.AddOutgoingDocument(document);
            if (id > 0)
            {
                ShowSuccessMessage("تم إضافة الوثيقة الصادرة بنجاح! ✅");
                LoadOutgoingDocuments();
                ClearOutgoingForm();
            }
            else
            {
                ShowErrorMessage("حدث خطأ أثناء إضافة الوثيقة! ❌");
            }
        }

        private void BtnRefreshIncoming_Click(object sender, EventArgs e)
        {
            LoadIncomingDocuments();
            ShowInfoMessage("تم تحديث قائمة الوثائق الواردة 🔄");
        }

        private void BtnRefreshOutgoing_Click(object sender, EventArgs e)
        {
            LoadOutgoingDocuments();
            ShowInfoMessage("تم تحديث قائمة الوثائق الصادرة 🔄");
        }

        private void LoadData()
        {
            LoadIncomingDocuments();
            LoadOutgoingDocuments();
        }

        private void LoadIncomingDocuments()
        {
            try
            {
                var documents = _dbHelper.GetAllIncomingDocuments();
                var displayData = documents.Select(d => new
                {
                    التسلسل = d.Id,
                    التاريخ = d.Date.ToString("yyyy/MM/dd"),
                    اسم_الدائرة = d.DepartmentName,
                    تاريخ_الوارد = d.IncomingDate.ToString("yyyy/MM/dd"),
                    العدد = d.Number,
                    الموضوع = d.Subject
                }).ToList();

                dgvIncoming.DataSource = displayData;
                FormatDataGridView(dgvIncoming);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في تحميل الوثائق الواردة: {ex.Message}");
            }
        }

        private void LoadOutgoingDocuments()
        {
            try
            {
                var documents = _dbHelper.GetAllOutgoingDocuments();
                var displayData = documents.Select(d => new
                {
                    التسلسل = d.Id,
                    التاريخ = d.Date.ToString("yyyy/MM/dd"),
                    اسم_الدائرة = d.DepartmentName,
                    الموضوع = d.Subject
                }).ToList();

                dgvOutgoing.DataSource = displayData;
                FormatDataGridView(dgvOutgoing);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"خطأ في تحميل الوثائق الصادرة: {ex.Message}");
            }
        }

        private void FormatDataGridView(DataGridView dgv)
        {
            if (dgv.Columns.Count > 0)
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
                }

                if (dgv.Columns["التسلسل"] != null)
                    dgv.Columns["التسلسل"].FillWeight = 10;
                if (dgv.Columns["التاريخ"] != null)
                    dgv.Columns["التاريخ"].FillWeight = 15;
                if (dgv.Columns["اسم_الدائرة"] != null)
                    dgv.Columns["اسم_الدائرة"].FillWeight = 25;
                if (dgv.Columns["الموضوع"] != null)
                    dgv.Columns["الموضوع"].FillWeight = 40;
            }
        }

        private bool ValidateIncomingInput()
        {
            if (string.IsNullOrWhiteSpace(txtIncomingDepartment.Text))
            {
                ShowWarningMessage("يرجى إدخال اسم الدائرة! ⚠️");
                txtIncomingDepartment.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtIncomingNumber.Text))
            {
                ShowWarningMessage("يرجى إدخال العدد! ⚠️");
                txtIncomingNumber.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtIncomingSubject.Text))
            {
                ShowWarningMessage("يرجى إدخال الموضوع! ⚠️");
                txtIncomingSubject.Focus();
                return false;
            }

            return true;
        }

        private bool ValidateOutgoingInput()
        {
            if (string.IsNullOrWhiteSpace(txtOutgoingDepartment.Text))
            {
                ShowWarningMessage("يرجى إدخال اسم الدائرة! ⚠️");
                txtOutgoingDepartment.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtOutgoingSubject.Text))
            {
                ShowWarningMessage("يرجى إدخال الموضوع! ⚠️");
                txtOutgoingSubject.Focus();
                return false;
            }

            return true;
        }

        private void ClearIncomingForm()
        {
            txtIncomingDepartment.Clear();
            txtIncomingNumber.Clear();
            txtIncomingSubject.Clear();
            dtpIncomingDate.Value = DateTime.Today;
            dtpIncomingDocDate.Value = DateTime.Today;
        }

        private void ClearOutgoingForm()
        {
            txtOutgoingDepartment.Clear();
            txtOutgoingSubject.Clear();
            dtpOutgoingDate.Value = DateTime.Today;
        }

        private void ShowSuccessMessage(string message)
        {
            MessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private void ShowWarningMessage(string message)
        {
            MessageBox.Show(message, "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private void ShowInfoMessage(string message)
        {
            MessageBox.Show(message, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
