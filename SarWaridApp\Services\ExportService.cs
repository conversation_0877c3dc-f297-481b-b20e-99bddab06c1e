using System.Text;
using SarWaridApp.Models;
using SarWaridApp.Extensions;

namespace SarWaridApp.Services
{
    public class ExportService
    {
        public static bool ExportToCsv(List<Document> documents, string filePath)
        {
            try
            {
                var csv = new StringBuilder();
                
                // Add header
                csv.AppendLine("التسلسل,التاريخ,اسم الدائرة,الموضوع,النوع,تاريخ الإنشاء,تاريخ التحديث");
                
                // Add data
                foreach (var doc in documents)
                {
                    csv.AppendLine($"{doc.Id}," +
                                 $"{doc.Date.GetFormattedDate()}," +
                                 $"\"{doc.DepartmentName}\"," +
                                 $"\"{doc.Subject}\"," +
                                 $"{doc.Type.GetTypeDisplayName()}," +
                                 $"{doc.CreatedAt.GetFormattedDateTime()}," +
                                 $"{doc.UpdatedAt.GetFormattedDateTime()}");
                }
                
                File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public static bool ExportToHtml(List<Document> documents, string filePath)
        {
            try
            {
                var html = new StringBuilder();
                html.AppendLine("<!DOCTYPE html>");
                html.AppendLine("<html dir='rtl'>");
                html.AppendLine("<head>");
                html.AppendLine("<meta charset='UTF-8'>");
                html.AppendLine("<title>تقرير الصادر والوارد</title>");
                html.AppendLine("<style>");
                html.AppendLine("body { font-family: 'Tahoma', Arial, sans-serif; direction: rtl; }");
                html.AppendLine("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
                html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }");
                html.AppendLine("th { background-color: #f2f2f2; font-weight: bold; }");
                html.AppendLine("tr:nth-child(even) { background-color: #f9f9f9; }");
                html.AppendLine("h1 { text-align: center; color: #333; }");
                html.AppendLine("</style>");
                html.AppendLine("</head>");
                html.AppendLine("<body>");
                html.AppendLine("<h1>تقرير الصادر والوارد</h1>");
                html.AppendLine($"<p>تاريخ التقرير: {DateTime.Now.GetFormattedDateTime()}</p>");
                html.AppendLine($"<p>عدد الوثائق: {documents.Count}</p>");
                html.AppendLine("<table>");
                html.AppendLine("<thead>");
                html.AppendLine("<tr>");
                html.AppendLine("<th>التسلسل</th>");
                html.AppendLine("<th>التاريخ</th>");
                html.AppendLine("<th>اسم الدائرة</th>");
                html.AppendLine("<th>الموضوع</th>");
                html.AppendLine("<th>النوع</th>");
                html.AppendLine("<th>تاريخ الإنشاء</th>");
                html.AppendLine("</tr>");
                html.AppendLine("</thead>");
                html.AppendLine("<tbody>");

                foreach (var doc in documents)
                {
                    html.AppendLine("<tr>");
                    html.AppendLine($"<td>{doc.Id}</td>");
                    html.AppendLine($"<td>{doc.Date.GetFormattedDate()}</td>");
                    html.AppendLine($"<td>{doc.DepartmentName}</td>");
                    html.AppendLine($"<td>{doc.Subject}</td>");
                    html.AppendLine($"<td>{doc.Type.GetTypeDisplayName()}</td>");
                    html.AppendLine($"<td>{doc.CreatedAt.GetFormattedDateTime()}</td>");
                    html.AppendLine("</tr>");
                }

                html.AppendLine("</tbody>");
                html.AppendLine("</table>");
                html.AppendLine("</body>");
                html.AppendLine("</html>");

                File.WriteAllText(filePath, html.ToString(), Encoding.UTF8);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public static void PrintDocuments(List<Document> documents)
        {
            // This would require additional printing libraries
            // For now, we'll export to HTML and let the user print from browser
            var tempPath = Path.Combine(Path.GetTempPath(), "documents_report.html");
            if (ExportToHtml(documents, tempPath))
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempPath,
                    UseShellExecute = true
                });
            }
        }
    }
}
