🚀 دليل الاستخدام السريع - نظام إدارة الصادر والوارد الحديث
================================================================

🎯 طريقة التشغيل:
-----------------
1. انقر نقراً مزدوجاً على ملف "تشغيل_البرنامج.bat"
   أو
2. افتح Command Prompt في مجلد البرنامج واكتب:
   dotnet run

✅ البرنامج الحديث يعمل الآن بنجاح!

📋 التصميم الجديد:
-----------------
- تبويب منفصل للوثائق الواردة والصادرة
- تصميم حديث بألوان جميلة
- واجهة أكبر وأوضح
- رسائل تفاعلية مع رموز

📥 حقول الوثائق الواردة (6 حقول):
----------------------------------
1. التسلسل: رقم تلقائي (لا يحتاج إدخال)
2. التاريخ: تاريخ تلقائي (يمكن تعديله)
3. اسم الدائرة: أدخل اسم الدائرة المرسلة
4. تاريخ الوارد: تاريخ استلام الوثيقة
5. العدد: رقم الوثيقة
6. الموضوع: أدخل موضوع الوثيقة

📤 حقول الوثائق الصادرة (4 حقول):
----------------------------------
1. التسلسل: رقم تلقائي (لا يحتاج إدخال)
2. التاريخ: تاريخ تلقائي (يمكن تعديله)
3. اسم الدائرة: أدخل اسم الدائرة المستقبلة
4. الموضوع: أدخل موضوع الوثيقة

خطوات إضافة وثيقة جديدة:
------------------------
1. أدخل اسم الدائرة
2. أدخل الموضوع
3. اختر التاريخ (افتراضياً اليوم)
4. اختر النوع (وارد/صادر)
5. اضغط "إضافة"

خطوات تعديل وثيقة:
------------------
1. اختر الوثيقة من الجدول
2. ستظهر بياناتها في النموذج
3. عدل البيانات المطلوبة
4. اضغط "تعديل"

خطوات حذف وثيقة:
-----------------
1. اختر الوثيقة من الجدول
2. اضغط "حذف"
3. أكد الحذف

خطوات البحث:
-------------
1. أدخل كلمة البحث
2. اختر نوع الوثيقة (اختياري)
3. اضغط "بحث"

التصدير والطباعة:
-----------------
- تصدير CSV: لحفظ البيانات في ملف Excel
- تصدير HTML: لإنشاء تقرير منسق
- طباعة: لطباعة التقرير

أزرار أخرى:
-----------
- مسح: لمسح النموذج
- تحديث: لتحديث قائمة الوثائق

ملاحظات مهمة:
--------------
- يتم حفظ البيانات تلقائياً في ملف documents.db
- يمكن نسخ هذا الملف لعمل نسخة احتياطية
- البرنامج يدعم اللغة العربية بالكامل
- جميع التواريخ بالتقويم الميلادي

للدعم الفني:
-------------
في حالة وجود مشاكل، تأكد من:
1. وجود صلاحيات الكتابة في مجلد البرنامج
2. عدم فتح ملف قاعدة البيانات في برنامج آخر
3. توفر مساحة كافية على القرص الصلب
