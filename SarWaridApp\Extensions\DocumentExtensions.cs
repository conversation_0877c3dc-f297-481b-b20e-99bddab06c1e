using SarWaridApp.Models;

namespace SarWaridApp.Extensions
{
    public static class DocumentExtensions
    {
        public static string GetTypeDisplayName(this DocumentType type)
        {
            return type switch
            {
                DocumentType.Incoming => "وارد",
                DocumentType.Outgoing => "صادر",
                _ => "غير محدد"
            };
        }

        public static string GetFormattedDate(this DateTime date)
        {
            return date.ToString("yyyy/MM/dd");
        }

        public static string GetFormattedDateTime(this DateTime dateTime)
        {
            return dateTime.ToString("yyyy/MM/dd HH:mm:ss");
        }
    }
}
