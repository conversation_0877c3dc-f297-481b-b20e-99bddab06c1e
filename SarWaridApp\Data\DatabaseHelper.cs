using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.Sqlite;
using SarWaridApp.Models;

namespace SarWaridApp.Data
{
    public class DatabaseHelper
    {
        private readonly string _connectionString;

        public DatabaseHelper()
        {
            _connectionString = "Data Source=documents.db";
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var createTableCommand = connection.CreateCommand();
            createTableCommand.CommandText = @"
                CREATE TABLE IF NOT EXISTS Documents (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Date TEXT NOT NULL,
                    DepartmentName TEXT NOT NULL,
                    Subject TEXT NOT NULL,
                    Type INTEGER NOT NULL,
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NOT NULL
                )";
            createTableCommand.ExecuteNonQuery();
        }

        public int AddDocument(Document document)
        {
            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT INTO Documents (Date, DepartmentName, Subject, Type, CreatedAt, UpdatedAt)
                VALUES (@date, @departmentName, @subject, @type, @createdAt, @updatedAt);
                SELECT last_insert_rowid();";

            command.Parameters.AddWithValue("@date", document.Date.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@departmentName", document.DepartmentName);
            command.Parameters.AddWithValue("@subject", document.Subject);
            command.Parameters.AddWithValue("@type", (int)document.Type);
            command.Parameters.AddWithValue("@createdAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@updatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            return Convert.ToInt32(command.ExecuteScalar());
        }

        public List<Document> GetAllDocuments()
        {
            var documents = new List<Document>();

            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = "SELECT * FROM Documents ORDER BY Id DESC";

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                documents.Add(new Document
                {
                    Id = reader.GetInt32("Id"),
                    Date = DateTime.Parse(reader.GetString("Date")),
                    DepartmentName = reader.GetString("DepartmentName"),
                    Subject = reader.GetString("Subject"),
                    Type = (DocumentType)reader.GetInt32("Type"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt")),
                    UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"))
                });
            }

            return documents;
        }

        public bool UpdateDocument(Document document)
        {
            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = @"
                UPDATE Documents 
                SET Date = @date, DepartmentName = @departmentName, Subject = @subject, 
                    Type = @type, UpdatedAt = @updatedAt
                WHERE Id = @id";

            command.Parameters.AddWithValue("@id", document.Id);
            command.Parameters.AddWithValue("@date", document.Date.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@departmentName", document.DepartmentName);
            command.Parameters.AddWithValue("@subject", document.Subject);
            command.Parameters.AddWithValue("@type", (int)document.Type);
            command.Parameters.AddWithValue("@updatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            return command.ExecuteNonQuery() > 0;
        }

        public bool DeleteDocument(int id)
        {
            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = "DELETE FROM Documents WHERE Id = @id";
            command.Parameters.AddWithValue("@id", id);

            return command.ExecuteNonQuery() > 0;
        }

        public List<Document> SearchDocuments(string searchTerm, DocumentType? type = null)
        {
            var documents = new List<Document>();

            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            var whereClause = "WHERE (DepartmentName LIKE @searchTerm OR Subject LIKE @searchTerm)";
            
            if (type.HasValue)
            {
                whereClause += " AND Type = @type";
            }

            command.CommandText = $"SELECT * FROM Documents {whereClause} ORDER BY Id DESC";
            command.Parameters.AddWithValue("@searchTerm", $"%{searchTerm}%");
            
            if (type.HasValue)
            {
                command.Parameters.AddWithValue("@type", (int)type.Value);
            }

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                documents.Add(new Document
                {
                    Id = reader.GetInt32("Id"),
                    Date = DateTime.Parse(reader.GetString("Date")),
                    DepartmentName = reader.GetString("DepartmentName"),
                    Subject = reader.GetString("Subject"),
                    Type = (DocumentType)reader.GetInt32("Type"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt")),
                    UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"))
                });
            }

            return documents;
        }
    }
}
