using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.Sqlite;
using SarWaridApp.Models;

namespace SarWaridApp.Data
{
    public class DatabaseHelper
    {
        private readonly string _connectionString;

        public DatabaseHelper()
        {
            _connectionString = "Data Source=documents.db";
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            // Create Incoming Documents table
            var createIncomingTableCommand = connection.CreateCommand();
            createIncomingTableCommand.CommandText = @"
                CREATE TABLE IF NOT EXISTS IncomingDocuments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Date TEXT NOT NULL,
                    DepartmentName TEXT NOT NULL,
                    IncomingDate TEXT NOT NULL,
                    Number TEXT NOT NULL,
                    Subject TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NOT NULL
                )";
            createIncomingTableCommand.ExecuteNonQuery();

            // Create Outgoing Documents table
            var createOutgoingTableCommand = connection.CreateCommand();
            createOutgoingTableCommand.CommandText = @"
                CREATE TABLE IF NOT EXISTS OutgoingDocuments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Date TEXT NOT NULL,
                    DepartmentName TEXT NOT NULL,
                    Subject TEXT NOT NULL,
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NOT NULL
                )";
            createOutgoingTableCommand.ExecuteNonQuery();
        }

        public int AddIncomingDocument(IncomingDocument document)
        {
            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT INTO IncomingDocuments (Date, DepartmentName, IncomingDate, Number, Subject, CreatedAt, UpdatedAt)
                VALUES (@date, @departmentName, @incomingDate, @number, @subject, @createdAt, @updatedAt);
                SELECT last_insert_rowid();";

            command.Parameters.AddWithValue("@date", document.Date.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@departmentName", document.DepartmentName);
            command.Parameters.AddWithValue("@incomingDate", document.IncomingDate.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@number", document.Number);
            command.Parameters.AddWithValue("@subject", document.Subject);
            command.Parameters.AddWithValue("@createdAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@updatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            return Convert.ToInt32(command.ExecuteScalar());
        }

        public int AddOutgoingDocument(OutgoingDocument document)
        {
            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = @"
                INSERT INTO OutgoingDocuments (Date, DepartmentName, Subject, CreatedAt, UpdatedAt)
                VALUES (@date, @departmentName, @subject, @createdAt, @updatedAt);
                SELECT last_insert_rowid();";

            command.Parameters.AddWithValue("@date", document.Date.ToString("yyyy-MM-dd"));
            command.Parameters.AddWithValue("@departmentName", document.DepartmentName);
            command.Parameters.AddWithValue("@subject", document.Subject);
            command.Parameters.AddWithValue("@createdAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@updatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

            return Convert.ToInt32(command.ExecuteScalar());
        }

        public List<IncomingDocument> GetAllIncomingDocuments()
        {
            var documents = new List<IncomingDocument>();

            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = "SELECT * FROM IncomingDocuments ORDER BY Id DESC";

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                documents.Add(new IncomingDocument
                {
                    Id = reader.GetInt32("Id"),
                    Date = DateTime.Parse(reader.GetString("Date")),
                    DepartmentName = reader.GetString("DepartmentName"),
                    IncomingDate = DateTime.Parse(reader.GetString("IncomingDate")),
                    Number = reader.GetString("Number"),
                    Subject = reader.GetString("Subject"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt")),
                    UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"))
                });
            }

            return documents;
        }

        public List<OutgoingDocument> GetAllOutgoingDocuments()
        {
            var documents = new List<OutgoingDocument>();

            using var connection = new SqliteConnection(_connectionString);
            connection.Open();

            var command = connection.CreateCommand();
            command.CommandText = "SELECT * FROM OutgoingDocuments ORDER BY Id DESC";

            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                documents.Add(new OutgoingDocument
                {
                    Id = reader.GetInt32("Id"),
                    Date = DateTime.Parse(reader.GetString("Date")),
                    DepartmentName = reader.GetString("DepartmentName"),
                    Subject = reader.GetString("Subject"),
                    CreatedAt = DateTime.Parse(reader.GetString("CreatedAt")),
                    UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"))
                });
            }

            return documents;
        }


    }
}
