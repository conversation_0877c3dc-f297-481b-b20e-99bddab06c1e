using SarWaridApp.Data;
using SarWaridApp.Models;
using SarWaridApp.Extensions;
using SarWaridApp.Services;

namespace SarWaridApp;

public partial class Form1 : Form
{
    private DatabaseHelper _dbHelper;
    private Document? _selectedDocument;

    public Form1()
    {
        InitializeComponent();
        _dbHelper = new DatabaseHelper();
        InitializeForm();
        LoadDocuments();
    }

    private void InitializeForm()
    {
        this.Text = "نظام إدارة الصادر والوارد";
        this.Size = new Size(1000, 700);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.RightToLeft = RightToLeft.Yes;
        this.RightToLeftLayout = true;

        // Set today's date by default
        dateTimePicker1.Value = DateTime.Today;

        // Clear form
        ClearForm();
    }

    private void LoadDocuments()
    {
        var documents = _dbHelper.GetAllDocuments();

        // Create a display list with formatted data
        var displayDocuments = documents.Select(d => new
        {
            التسلسل = d.Id,
            التاريخ = d.Date.GetFormattedDate(),
            اسم_الدائرة = d.DepartmentName,
            الموضوع = d.Subject,
            النوع = d.Type.GetTypeDisplayName(),
            تاريخ_الإنشاء = d.CreatedAt.GetFormattedDateTime(),
            تاريخ_التحديث = d.UpdatedAt.GetFormattedDateTime(),
            OriginalDocument = d // Keep reference to original document
        }).ToList();

        dataGridView1.DataSource = displayDocuments;

        // Configure DataGridView columns
        if (dataGridView1.Columns.Count > 0)
        {
            // Hide some columns
            if (dataGridView1.Columns["تاريخ_الإنشاء"] != null)
                dataGridView1.Columns["تاريخ_الإنشاء"].Visible = false;
            if (dataGridView1.Columns["تاريخ_التحديث"] != null)
                dataGridView1.Columns["تاريخ_التحديث"].Visible = false;
            if (dataGridView1.Columns["OriginalDocument"] != null)
                dataGridView1.Columns["OriginalDocument"].Visible = false;

            // Set column widths
            if (dataGridView1.Columns["التسلسل"] != null)
                dataGridView1.Columns["التسلسل"].Width = 80;
            if (dataGridView1.Columns["التاريخ"] != null)
                dataGridView1.Columns["التاريخ"].Width = 100;
            if (dataGridView1.Columns["اسم_الدائرة"] != null)
                dataGridView1.Columns["اسم_الدائرة"].Width = 200;
            if (dataGridView1.Columns["الموضوع"] != null)
                dataGridView1.Columns["الموضوع"].Width = 400;
            if (dataGridView1.Columns["النوع"] != null)
                dataGridView1.Columns["النوع"].Width = 80;
        }
    }

    private void ClearForm()
    {
        textBoxDepartment.Clear();
        textBoxSubject.Clear();
        dateTimePicker1.Value = DateTime.Today;
        radioButtonIncoming.Checked = true;
        _selectedDocument = null;
        buttonUpdate.Enabled = false;
        buttonDelete.Enabled = false;
        buttonAdd.Enabled = true;
    }

    private void buttonAdd_Click(object sender, EventArgs e)
    {
        if (ValidateInput())
        {
            var document = new Document
            {
                Date = dateTimePicker1.Value.Date,
                DepartmentName = textBoxDepartment.Text.Trim(),
                Subject = textBoxSubject.Text.Trim(),
                Type = radioButtonIncoming.Checked ? DocumentType.Incoming : DocumentType.Outgoing
            };

            var id = _dbHelper.AddDocument(document);
            if (id > 0)
            {
                MessageBox.Show("تم إضافة الوثيقة بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadDocuments();
                ClearForm();
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء إضافة الوثيقة!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void buttonUpdate_Click(object sender, EventArgs e)
    {
        if (_selectedDocument != null && ValidateInput())
        {
            _selectedDocument.Date = dateTimePicker1.Value.Date;
            _selectedDocument.DepartmentName = textBoxDepartment.Text.Trim();
            _selectedDocument.Subject = textBoxSubject.Text.Trim();
            _selectedDocument.Type = radioButtonIncoming.Checked ? DocumentType.Incoming : DocumentType.Outgoing;

            if (_dbHelper.UpdateDocument(_selectedDocument))
            {
                MessageBox.Show("تم تحديث الوثيقة بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadDocuments();
                ClearForm();
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء تحديث الوثيقة!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void buttonDelete_Click(object sender, EventArgs e)
    {
        if (_selectedDocument != null)
        {
            var result = MessageBox.Show("هل أنت متأكد من حذف هذه الوثيقة؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                if (_dbHelper.DeleteDocument(_selectedDocument.Id))
                {
                    MessageBox.Show("تم حذف الوثيقة بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadDocuments();
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("حدث خطأ أثناء حذف الوثيقة!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }

    private void buttonSearch_Click(object sender, EventArgs e)
    {
        var searchTerm = textBoxSearch.Text.Trim();
        if (!string.IsNullOrEmpty(searchTerm))
        {
            DocumentType? type = null;
            if (radioButtonSearchIncoming.Checked)
                type = DocumentType.Incoming;
            else if (radioButtonSearchOutgoing.Checked)
                type = DocumentType.Outgoing;

            var documents = _dbHelper.SearchDocuments(searchTerm, type);

            // Create a display list with formatted data
            var displayDocuments = documents.Select(d => new
            {
                التسلسل = d.Id,
                التاريخ = d.Date.GetFormattedDate(),
                اسم_الدائرة = d.DepartmentName,
                الموضوع = d.Subject,
                النوع = d.Type.GetTypeDisplayName(),
                تاريخ_الإنشاء = d.CreatedAt.GetFormattedDateTime(),
                تاريخ_التحديث = d.UpdatedAt.GetFormattedDateTime(),
                OriginalDocument = d
            }).ToList();

            dataGridView1.DataSource = displayDocuments;
        }
        else
        {
            LoadDocuments();
        }
    }

    private void buttonClear_Click(object sender, EventArgs e)
    {
        ClearForm();
    }

    private void buttonRefresh_Click(object sender, EventArgs e)
    {
        LoadDocuments();
        textBoxSearch.Clear();
        radioButtonSearchAll.Checked = true;
    }

    private void dataGridView1_SelectionChanged(object sender, EventArgs e)
    {
        if (dataGridView1.SelectedRows.Count > 0)
        {
            var selectedRow = dataGridView1.SelectedRows[0];
            var selectedItem = selectedRow.DataBoundItem;

            // Get the original document from the anonymous type
            var originalDocumentProperty = selectedItem?.GetType().GetProperty("OriginalDocument");
            _selectedDocument = originalDocumentProperty?.GetValue(selectedItem) as Document;

            if (_selectedDocument != null)
            {
                dateTimePicker1.Value = _selectedDocument.Date;
                textBoxDepartment.Text = _selectedDocument.DepartmentName;
                textBoxSubject.Text = _selectedDocument.Subject;

                if (_selectedDocument.Type == DocumentType.Incoming)
                    radioButtonIncoming.Checked = true;
                else
                    radioButtonOutgoing.Checked = true;

                buttonUpdate.Enabled = true;
                buttonDelete.Enabled = true;
                buttonAdd.Enabled = false;
            }
        }
    }

    private bool ValidateInput()
    {
        if (string.IsNullOrWhiteSpace(textBoxDepartment.Text))
        {
            MessageBox.Show("يرجى إدخال اسم الدائرة!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            textBoxDepartment.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(textBoxSubject.Text))
        {
            MessageBox.Show("يرجى إدخال الموضوع!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            textBoxSubject.Focus();
            return false;
        }

        return true;
    }

    private void buttonExportCsv_Click(object sender, EventArgs e)
    {
        var saveFileDialog = new SaveFileDialog
        {
            Filter = "CSV files (*.csv)|*.csv",
            DefaultExt = "csv",
            FileName = $"documents_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
        };

        if (saveFileDialog.ShowDialog() == DialogResult.OK)
        {
            var documents = _dbHelper.GetAllDocuments();
            if (ExportService.ExportToCsv(documents, saveFileDialog.FileName))
            {
                MessageBox.Show("تم تصدير البيانات بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء تصدير البيانات!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void buttonExportHtml_Click(object sender, EventArgs e)
    {
        var saveFileDialog = new SaveFileDialog
        {
            Filter = "HTML files (*.html)|*.html",
            DefaultExt = "html",
            FileName = $"documents_report_{DateTime.Now:yyyyMMdd_HHmmss}.html"
        };

        if (saveFileDialog.ShowDialog() == DialogResult.OK)
        {
            var documents = _dbHelper.GetAllDocuments();
            if (ExportService.ExportToHtml(documents, saveFileDialog.FileName))
            {
                MessageBox.Show("تم تصدير التقرير بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Ask if user wants to open the file
                var result = MessageBox.Show("هل تريد فتح التقرير؟", "فتح التقرير",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = saveFileDialog.FileName,
                        UseShellExecute = true
                    });
                }
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء تصدير التقرير!", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void buttonPrint_Click(object sender, EventArgs e)
    {
        var documents = _dbHelper.GetAllDocuments();
        ExportService.PrintDocuments(documents);
    }
}
