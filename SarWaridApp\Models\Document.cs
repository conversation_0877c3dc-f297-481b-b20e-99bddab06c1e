using System;

namespace SarWaridApp.Models
{
    public class IncomingDocument
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public DateTime IncomingDate { get; set; }
        public string Number { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class OutgoingDocument
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public enum DocumentType
    {
        Incoming = 1,  // وارد
        Outgoing = 2   // صادر
    }
}
