using System;

namespace SarWaridApp.Models
{
    public class Document
    {
        public int Id { get; set; }
        public DateTime Date { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public DocumentType Type { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public enum DocumentType
    {
        Incoming = 1,  // وارد
        Outgoing = 2   // صادر
    }
}
