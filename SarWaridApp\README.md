# 🚀 نظام إدارة الصادر والوارد - الإصدار الحديث

برنامج حديث وجميل بلغة C# لإدارة الوثائق الصادرة والواردة في المؤسسات والدوائر الحكومية.

## ✨ المميزات الجديدة:
- **تصميم حديث وجميل** مع ألوان عصرية
- **واجهة تبويب منفصلة** للوارد والصادر
- **حقول منفصلة ومخصصة** لكل نوع وثيقة
- **تخطيط احترافي** مع مساحات مناسبة
- **رسائل تفاعلية** مع رموز تعبيرية

## ✅ البرنامج يعمل بنجاح!

### 🚀 طريقة التشغيل السريعة:
1. **انقر نقراً مزدوجاً على ملف `تشغيل_البرنامج.bat`**
2. أو افتح Command Prompt واكتب: `dotnet run`

## الميزات

### 📥 حقول الوثائق الواردة (6 حقول):
1. **التسلسل**: رقم تلقائي متزايد
2. **التاريخ**: تاريخ تلقائي (قابل للتعديل)
3. **اسم الدائرة**: اسم الدائرة المرسلة
4. **تاريخ الوارد**: تاريخ استلام الوثيقة
5. **العدد**: رقم الوثيقة
6. **الموضوع**: موضوع الوثيقة

### 📤 حقول الوثائق الصادرة (4 حقول):
1. **التسلسل**: رقم تلقائي متزايد
2. **التاريخ**: تاريخ تلقائي (قابل للتعديل)
3. **اسم الدائرة**: اسم الدائرة المستقبلة
4. **الموضوع**: موضوع الوثيقة

### العمليات المتاحة
- **إضافة**: إضافة وثيقة جديدة
- **تعديل**: تعديل وثيقة موجودة
- **حذف**: حذف وثيقة مع تأكيد
- **مسح**: مسح النموذج لإدخال جديد
- **تحديث**: تحديث قائمة الوثائق

### البحث والتصفية
- البحث في اسم الدائرة والموضوع
- تصفية حسب نوع الوثيقة (وارد/صادر/الكل)
- عرض جميع الوثائق مرتبة حسب التسلسل

## متطلبات التشغيل

- .NET 9.0 أو أحدث
- Windows Operating System
- SQLite (مدمج مع البرنامج)

## كيفية التشغيل

### من خلال Visual Studio
1. افتح المشروع في Visual Studio
2. اضغط F5 أو اختر Debug > Start Debugging

### من خلال سطر الأوامر
```bash
cd SarWaridApp
dotnet run
```

### بناء ملف تنفيذي
```bash
cd SarWaridApp
dotnet publish -c Release -r win-x64 --self-contained
```

## كيفية الاستخدام

### إضافة وثيقة جديدة
1. أدخل اسم الدائرة في الحقل المخصص
2. أدخل موضوع الوثيقة
3. اختر التاريخ (افتراضياً تاريخ اليوم)
4. اختر نوع الوثيقة (وارد أو صادر)
5. اضغط زر "إضافة"

### تعديل وثيقة موجودة
1. اختر الوثيقة من الجدول
2. ستظهر بياناتها في النموذج
3. عدل البيانات المطلوبة
4. اضغط زر "تعديل"

### حذف وثيقة
1. اختر الوثيقة من الجدول
2. اضغط زر "حذف"
3. أكد الحذف في النافذة المنبثقة

### البحث
1. أدخل كلمة البحث في حقل البحث
2. اختر نوع الوثيقة للتصفية (اختياري)
3. اضغط زر "بحث"

## هيكل قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite مع جدول واحد:

```sql
CREATE TABLE Documents (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Date TEXT NOT NULL,
    DepartmentName TEXT NOT NULL,
    Subject TEXT NOT NULL,
    Type INTEGER NOT NULL,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT NOT NULL
)
```

## الملفات الرئيسية

- `Form1.cs`: النموذج الرئيسي وواجهة المستخدم
- `Form1.Designer.cs`: تصميم واجهة المستخدم
- `Models/Document.cs`: نموذج البيانات
- `Data/DatabaseHelper.cs`: فئة التعامل مع قاعدة البيانات
- `documents.db`: ملف قاعدة البيانات (ينشأ تلقائياً)

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا البرنامج مجاني للاستخدام في المؤسسات الحكومية والخاصة.
