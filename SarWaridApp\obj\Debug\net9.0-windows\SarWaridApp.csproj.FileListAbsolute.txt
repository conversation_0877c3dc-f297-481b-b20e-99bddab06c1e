E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SarWaridApp.exe
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SarWaridApp.deps.json
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SarWaridApp.runtimeconfig.json
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SarWaridApp.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SarWaridApp.pdb
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\Microsoft.Data.Sqlite.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SQLitePCLRaw.batteries_v2.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SQLitePCLRaw.core.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\browser-wasm\nativeassets\net9.0\e_sqlite3.a
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-musl-s390x\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-ppc64le\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\win-arm\native\e_sqlite3.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\win-x64\native\e_sqlite3.dll
E:\SAR\SarWaridApp\bin\Debug\net9.0-windows\runtimes\win-x86\native\e_sqlite3.dll
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.csproj.AssemblyReference.cache
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.GeneratedMSBuildEditorConfig.editorconfig
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.AssemblyInfoInputs.cache
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.AssemblyInfo.cs
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.csproj.CoreCompileInputs.cache
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWarid.********.Up2Date
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.dll
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\refint\SarWaridApp.dll
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.pdb
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\SarWaridApp.genruntimeconfig.cache
E:\SAR\SarWaridApp\obj\Debug\net9.0-windows\ref\SarWaridApp.dll
